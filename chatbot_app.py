#!/usr/bin/env python3
"""
Streamlit Multimodal RAG Chatbot with Azure OpenAI
Allows users to upload PDFs and ask questions about them
"""

import streamlit as st
import os
import tempfile
import uuid
import shutil
import pickle
import hashlib
import logging
from datetime import datetime
from dotenv import load_dotenv

# Configure streamlit page
st.set_page_config(
    page_title="📄 Multimodal RAG Chatbot",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

def initialize_session_state():
    """Initialize session state variables"""
    if 'retriever' not in st.session_state:
        st.session_state.retriever = None
    if 'chat_history' not in st.session_state:
        st.session_state.chat_history = []
    if 'pdf_processed' not in st.session_state:
        st.session_state.pdf_processed = False
    if 'document_info' not in st.session_state:
        st.session_state.document_info = {}
    if 'process_logs' not in st.session_state:
        st.session_state.process_logs = []
    if 'batch_size' not in st.session_state:
        st.session_state.batch_size = 50
    if 'retrieval_count' not in st.session_state:
        st.session_state.retrieval_count = 50

def setup_logging():
    """Setup logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%H:%M:%S'
    )
    return logging.getLogger('streamlit_chatbot')

def log_to_ui(message, level="INFO"):
    """Add log message to UI display"""
    timestamp = datetime.now().strftime('%H:%M:%S')
    log_entry = f"[{timestamp}] {level}: {message}"
    st.session_state.process_logs.append(log_entry)
    
    # Keep only last 50 logs to avoid memory issues
    if len(st.session_state.process_logs) > 50:
        st.session_state.process_logs = st.session_state.process_logs[-50:]

def clear_logs():
    """Clear process logs"""
    st.session_state.process_logs = []

def setup_environment():
    """Setup environment and dependencies"""
    try:
        # Load environment variables
        load_dotenv()
        
        # Configure tesseract
        from tesseract_config import configure_tesseract
        configure_tesseract()
        
        # Import required modules
        from azure_oai_utils import AzOAIClientUtil
        
        # Get configuration
        endpoint = os.getenv("AZURE_OPENAI_ENDPOINT")
        api_version = os.getenv("AZURE_OPENAI_API_VERSION")
        deployment_name = os.getenv("AZURE_OPENAI_DEPLOYMENT_NAME")
        embedding_deployment = os.getenv("AZURE_OPENAI_EMBEDDING_DEPLOYMENT")
        
        if not all([endpoint, api_version, deployment_name, embedding_deployment]):
            st.error("❌ Missing Azure OpenAI configuration in .env file")
            return None
        
        # Initialize Azure client
        azure_client_util = AzOAIClientUtil(
            api_version=api_version,
            azure_endpoint=endpoint
        )
        
        return {
            'azure_client_util': azure_client_util,
            'endpoint': endpoint,
            'api_version': api_version,
            'deployment_name': deployment_name,
            'embedding_deployment': embedding_deployment
        }
        
    except Exception as e:
        st.error(f"❌ Environment setup failed: {e}")
        return None

@st.cache_data
def process_pdf(uploaded_file, _config):
    """Process uploaded PDF and extract content with hierarchical splitting"""
    logger = setup_logging()
    try:
        logger.info(f"Starting PDF processing for: {uploaded_file.name}")
        log_to_ui(f"Starting PDF processing for: {uploaded_file.name}")

        # Save uploaded file to temporary location
        log_to_ui("Saving PDF to temporary location...")
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as tmp_file:
            tmp_file.write(uploaded_file.getvalue())
            tmp_file_path = tmp_file.name
        logger.info(f"PDF saved to temporary path: {tmp_file_path}")

        # Import processing libraries
        from unstructured.partition.pdf import partition_pdf

        # Process PDF with hierarchical approach
        log_to_ui("Parsing PDF with hierarchical strategy...")
        logger.info("Starting PDF partition with hi_res strategy for hierarchical processing")

        # First pass: Get larger parent chunks
        parent_chunks = partition_pdf(
            filename=tmp_file_path,
            infer_table_structure=True,
            strategy="hi_res",
            extract_image_block_types=["Table"],
            extract_image_block_to_payload=True,
            chunking_strategy="by_title",
            max_characters=12000,  # Larger parent chunks
            combine_text_under_n_chars=3000,
            new_after_n_chars=10000,
            pdf_image_dpi=300,
            overlap=400
        )

        # Second pass: Create smaller child chunks
        child_chunks = partition_pdf(
            filename=tmp_file_path,
            infer_table_structure=True,
            strategy="hi_res",
            extract_image_block_types=["Table"],
            extract_image_block_to_payload=True,
            chunking_strategy="by_title",
            max_characters=4000,  # Smaller child chunks
            combine_text_under_n_chars=1000,
            new_after_n_chars=3000,
            pdf_image_dpi=300,
            overlap=200
        )

        logger.info(f"PDF partition completed - Parent chunks: {len(parent_chunks)}, Child chunks: {len(child_chunks)}")
        log_to_ui(f"Hierarchical parsing completed - Parent: {len(parent_chunks)}, Child: {len(child_chunks)} chunks")

        # Clean up temporary file
        os.unlink(tmp_file_path)
        logger.info("Temporary file cleaned up")

        # Extract and categorize content with hierarchy
        log_to_ui("Categorizing content types with hierarchical structure...")
        parent_tables, parent_texts = [], []
        child_tables, child_texts = [], []

        # Process parent chunks
        for i, chunk in enumerate(parent_chunks):
            # Add hierarchical metadata
            if hasattr(chunk, 'metadata'):
                chunk.metadata.hierarchy_level = "parent"
                chunk.metadata.parent_id = f"parent_{i}"
                chunk.metadata.chunk_index = i

            if "Table" in str(type(chunk)):
                parent_tables.append(chunk)
            elif "CompositeElement" in str(type(chunk)):
                parent_texts.append(chunk)

        # Process child chunks and link to parents
        for i, chunk in enumerate(child_chunks):
            # Find best matching parent based on content overlap
            parent_id = _find_parent_for_child(chunk, parent_chunks)

            if hasattr(chunk, 'metadata'):
                chunk.metadata.hierarchy_level = "child"
                chunk.metadata.parent_id = parent_id
                chunk.metadata.child_id = f"child_{i}"
                chunk.metadata.chunk_index = i

            if "Table" in str(type(chunk)):
                child_tables.append(chunk)
            elif "CompositeElement" in str(type(chunk)):
                child_texts.append(chunk)

        # Combine parent and child chunks for processing
        all_chunks = parent_chunks + child_chunks
        all_texts = parent_texts + child_texts
        all_tables = parent_tables + child_tables

        # Extract images
        def get_images_base64(chunks):
            images_b64 = []
            for chunk in chunks:
                if "CompositeElement" in str(type(chunk)):
                    if hasattr(chunk.metadata, 'orig_elements') and chunk.metadata.orig_elements:
                        chunk_els = chunk.metadata.orig_elements
                        for el in chunk_els:
                            if "Image" in str(type(el)):
                                if hasattr(el.metadata, 'image_base64') and el.metadata.image_base64:
                                    images_b64.append(el.metadata.image_base64)
                    elif hasattr(chunk.metadata, 'image_base64') and chunk.metadata.image_base64:
                        images_b64.append(chunk.metadata.image_base64)
            return images_b64

        log_to_ui("Extracting images from PDF...")
        images = get_images_base64(all_chunks)

        # Log final results
        result_summary = f"Hierarchical content extracted - Parent Texts: {len(parent_texts)}, Child Texts: {len(child_texts)}, Parent Tables: {len(parent_tables)}, Child Tables: {len(child_tables)}, Images: {len(images)}"
        logger.info(result_summary)
        log_to_ui(result_summary)
        log_to_ui("✅ PDF processing completed successfully")

        return {
            'chunks': all_chunks,
            'texts': all_texts,
            'tables': all_tables,
            'images': images,
            'parent_texts': parent_texts,
            'child_texts': child_texts,
            'parent_tables': parent_tables,
            'child_tables': child_tables,
            'total_chunks': len(all_chunks),
            'text_chunks': len(all_texts),
            'table_chunks': len(all_tables),
            'image_chunks': len(images)
        }
        
    except Exception as e:
        error_msg = f"PDF processing failed: {e}"
        logger.error(error_msg)
        log_to_ui(error_msg, "ERROR")
        st.error(f"❌ {error_msg}")
        return None

def generate_summaries(content, config, batch_size=50):
    """Generate summaries for extracted content"""
    logger = setup_logging()
    try:
        azure_client_util = config['azure_client_util']
        deployment_name = config['deployment_name']
        
        progress_bar = st.progress(0)
        status_text = st.empty()
        
        # Calculate total items
        total_items = len(content['texts']) + len(content['tables']) + len(content['images'])
        current_item = 0
        
        logger.info(f"Starting summary generation for {total_items} items with batch size {batch_size}")
        log_to_ui(f"Starting summary generation for {total_items} items (Texts: {len(content['texts'])}, Tables: {len(content['tables'])}, Images: {len(content['images'])})")
        
        if total_items > batch_size:
            log_to_ui(f"Processing in batches of {batch_size} due to large number of items")
        
        # Summarize texts
        text_summaries = []
        if content['texts']:
            status_text.text("📝 Summarizing text chunks...")
            logger.info(f"Summarizing {len(content['texts'])} text chunks")
            log_to_ui(f"📝 Processing {len(content['texts'])} text chunks...")
            
            # Process in batches
            for batch_start in range(0, len(content['texts']), batch_size):
                batch_end = min(batch_start + batch_size, len(content['texts']))
                batch_texts = content['texts'][batch_start:batch_end]
                
                if len(content['texts']) > batch_size:
                    log_to_ui(f"Processing text batch {batch_start//batch_size + 1}/{(len(content['texts']) + batch_size - 1)//batch_size}")
                
                for i, text in enumerate(batch_texts):
                    global_index = batch_start + i
                    try:
                        log_to_ui(f"Summarizing text chunk {global_index+1}/{len(content['texts'])}")
                        client, _, _, _ = azure_client_util.get_azoai_client()

                        # System prompt for drilling expert role
                        text_system_prompt = (
                            "You are an expert drilling engineer with extensive experience analyzing drilling reports. "
                            "Your task is to extract and summarize key operational information from drilling reports "
                            "with precision and technical accuracy. Focus only on factual, technical information "
                            "that would be relevant for drilling operations personnel."
                            )

                        
                        # Specialized prompt for drilling report text
                        text_user_prompt = (
                            "Extract and summarize key drilling operational information from this text, focusing on: "
                            "\n1. Well operations (drilling, tripping, testing, completions)"
                            "\n2. Technical parameters (depths, pressures, volumes, rates, temperatures)"
                            "\n3. Equipment status and issues"
                            "\n4. Safety and HSE observations and incidents"
                            "\n5. Operational challenges and mitigations"
                            "\n6. Formation evaluation and geological observations"
                            "\n7. Fluid properties and treatments"
                            "\n8. Well information (well name, location, wellbore, etc.)"
                            "\n9. Performance metrics (wellbore completion, flow rate, etc.)"
                            "\n10. Well Control Comments and related information"
                            "\n11. Last 24hr Summary if present. "
                            "\n12. Any other relevant information that can be extracted from the text."
                            "\n\nEnsure that the summary is concise, free of jargon, and includes all necessary information."
                            "\n\nInclude all numerical values with their units. "
                            "Preserve technical terminology and jargon exactly as written. "
                            "Format as a concise, factual summary focused only on drilling and related operations."
                            "\n\n"
                            f"{text.text}"
                        )
                        
                        response = client.chat.completions.create(
                        model=deployment_name,
                        messages=[
                            {"role": "system", "content": text_system_prompt},
                            {"role": "user", "content": text_user_prompt}
                        ],
                        temperature=0.1,
                        max_tokens=1000
                            )
                        
                        summary = response.choices[0].message.content
                        text_summaries.append(summary)
                        current_item += 1
                        progress_bar.progress(current_item / total_items if total_items > 0 else 1.0)
                        logger.info(f"Text chunk {global_index+1} summarized successfully")
                    except Exception as e:
                        error_msg = f"Error summarizing text {global_index+1}: {e}"
                        logger.warning(error_msg)
                        log_to_ui(error_msg, "WARNING")
                        st.warning(f"⚠️ {error_msg}")
                        text_summaries.append(f"Summary unavailable for text {global_index+1}")
        
        # Summarize tables - with batching and optimized parameters
        table_summaries = []
        if content['tables']:
            status_text.text("📊 Summarizing tables...")
            logger.info(f"Summarizing {len(content['tables'])} tables")
            log_to_ui(f"📊 Processing {len(content['tables'])} tables...")
            
            # Process tables in batches
            for batch_start in range(0, len(content['tables']), batch_size):
                batch_end = min(batch_start + batch_size, len(content['tables']))
                batch_tables = content['tables'][batch_start:batch_end]
                
                if len(content['tables']) > batch_size:
                    log_to_ui(f"Processing table batch {batch_start//batch_size + 1}/{(len(content['tables']) + batch_size - 1)//batch_size}")
                
                for i, table in enumerate(batch_tables):
                    global_index = batch_start + i
                    try:
                        log_to_ui(f"Summarizing table {global_index+1}/{len(content['tables'])}")
                        client, _, _, _ = azure_client_util.get_azoai_client()
                        table_html = table.metadata.text_as_html

                        # System prompt for drilling expert role
                        table_system_prompt = (
                            "You are an expert drilling engineer specializing in analyzing tabular data from drilling reports. "
                            "Your task is to extract and summarize key operational data from drilling report tables "
                            "with technical precision. Focus on extracting structured information that preserves "
                            "numerical values, relationships between parameters, and operational context."
                        )
                        
                        # Optimized prompt for drilling report tables
                        table_user_prompt = (
                            "Extract and summarize the key drilling operational data from this table, focusing on: "
                            "\n1. Well information (well name, coordinates, elevations, field/block details)"
                            "\n2. Current operations (phase, activity, NPT details, time logs)"
                            "\n3. Critical parameters (pressures, depths, volumes, flow rates, temperatures)"
                            "\n4. Well control information (barriers, tests, BOP status)"
                            "\n5. Safety incidents, STOP cards, and JSAs"
                            "\n6. Equipment status and upcoming maintenance/swap dates"
                            "\n7. Operational challenges (loss, stuck pipe, mud loss, circulation issues etc) or deviations mentioned"
                            "\n8. Note PST (Pressure Safety Tests), perforations, casing, and cementing operations, drilling, tripping, mud circulating, testing, and other related operations"
                            "\n9. Performance metrics (wellbore pressure, flow rate, drill rate, etc.)"
                            "\n10. Fluid Preparation & Treatments related information"
                            "\n11. Drilling phase information like PST01, PST02, PST03, PST04, etc."
                            "\n12. Drilling sections related information like 8 1/2 inch, 10 1/2 inch, 12 1/2 inch, 14 1/2 inch, etc."
                            "\n\nFormat your response as a structured summary with clear sections. "
                            "Include all numerical values with their units (psi, ft, bbl, bpm, etc.). "
                            "Preserve relationships between related measurements. "
                            "For time-based operations, note the date/time and duration. "
                            "Highlight any safety-critical information or well control issues. "
                            "Summarize any procedural notes that explain operational context."
                            "\n\n"
                            f"{table_html}"
                        )
                        
                        response = client.chat.completions.create(
                            model=deployment_name,
                            messages=[
                                {"role": "system", "content": table_system_prompt},
                                {"role": "user", "content": table_user_prompt}],
                            temperature=0.05,  # Lower temperature for more consistent numerical extraction
                            max_tokens=2000,  # Reduced from 16000 to optimize token usage
                            presence_penalty=0.1,  # Slight penalty to avoid repetition
                            frequency_penalty=0.1  # Slight penalty to encourage diversity
                        )
                        summary = response.choices[0].message.content
                        table_summaries.append(summary)
                        current_item += 1
                        progress_bar.progress(current_item / total_items if total_items > 0 else 1.0)
                        logger.info(f"Table {global_index+1} summarized successfully")
                    except Exception as e:
                        error_msg = f"Error summarizing table {global_index+1}: {e}"
                        logger.warning(error_msg)
                        log_to_ui(error_msg, "WARNING")
                        st.warning(f"⚠️ {error_msg}")
                        # Create a fallback summary with table dimensions instead of failing completely
                        fallback_summary = f"Table with approximately {table_html.count('<tr>')} rows and {table_html.count('<td>') // max(1, table_html.count('<tr>'))} columns. Content could not be summarized due to processing error."
                        table_summaries.append(fallback_summary)
                        current_item += 1
                        progress_bar.progress(current_item / total_items if total_items > 0 else 1.0)
        
        # Summarize images
        image_summaries = []
        if content['images']:
            status_text.text("🖼️ Analyzing images...")
            logger.info(f"Analyzing {len(content['images'])} images")
            log_to_ui(f"🖼️ Processing {len(content['images'])} images...")
            
            for i, image_b64 in enumerate(content['images']):
                try:
                    log_to_ui(f"Analyzing image {i+1}/{len(content['images'])}")
                    client, _, _, _ = azure_client_util.get_azoai_client()
                    messages = [{
                        "role": "user",
                        "content": [
                            {"type": "text", "text": "Describe this image in detail, focusing on key information that might be relevant for answering questions about the document."},
                            {
                                "type": "image_url",
                                "image_url": {"url": f"data:image/jpeg;base64,{image_b64}"}
                            }
                        ]
                    }]
                    
                    response = client.chat.completions.create(
                        model=deployment_name,
                        messages=messages,
                        temperature=0.5,
                        max_tokens=50
                    )
                    
                    summary = response.choices[0].message.content
                    image_summaries.append(summary)
                    current_item += 1
                    progress_bar.progress(current_item / total_items if total_items > 0 else 1.0)
                    logger.info(f"Image {i+1} analyzed successfully")
                except Exception as e:
                    error_msg = f"Error analyzing image {i+1}: {e}"
                    logger.warning(error_msg)
                    log_to_ui(error_msg, "WARNING")
                    st.warning(f"⚠️ {error_msg}")
                    image_summaries.append(f"Analysis unavailable for image {i+1}")
        
        progress_bar.progress(1.0)
        status_text.text("✅ Summary generation complete!")
        
        summary_stats = f"Summary generation completed - Text: {len(text_summaries)}, Tables: {len(table_summaries)}, Images: {len(image_summaries)}"
        logger.info(summary_stats)
        log_to_ui(f"✅ Summary generation completed successfully")
        log_to_ui(summary_stats)
        
        return text_summaries, table_summaries, image_summaries
        
    except Exception as e:
        error_msg = f"Summary generation failed: {e}"
        logger.error(error_msg)
        log_to_ui(error_msg, "ERROR")
        st.error(f"❌ {error_msg}")
        return None, None, None

def create_vector_store(content, text_summaries, table_summaries, image_summaries, config, pdf_filename=None):
    """Create hierarchical vector store with embeddings and hybrid search capabilities"""
    logger = setup_logging()
    try:
        logger.info("Starting hierarchical vector store creation")
        log_to_ui("🔧 Starting hierarchical vector store creation")

        from langchain_community.vectorstores import Chroma
        from langchain.storage import InMemoryStore
        from langchain.schema.document import Document
        from langchain.retrievers.multi_vector import MultiVectorRetriever
        from langchain_openai import AzureOpenAIEmbeddings
        from langchain_community.retrievers import BM25Retriever
        from langchain.retrievers import EnsembleRetriever

        # Create embeddings
        log_to_ui("Setting up Azure OpenAI embeddings...")
        azure_embeddings = AzureOpenAIEmbeddings(
            model=config['embedding_deployment'],
            azure_endpoint=config['endpoint'],
            api_version=config['api_version'],
            azure_ad_token_provider=lambda: config['azure_client_util'].get_azoai_client()[1]
        )
        logger.info("Azure embeddings configured")
        
        # Create collection name with filename+date+time format
        if pdf_filename:
            # Clean filename and add timestamp
            clean_filename = os.path.splitext(pdf_filename)[0].replace(' ', '_').replace('-', '_')
            timestamp = datetime.now().strftime("%d%m%Y_%H%M%S")
            collection_name = f"{clean_filename}_{timestamp}"
        else:
            collection_name = f"chatbot_rag_{uuid.uuid4().hex[:8]}"
        
        logger.info(f"Creating collection: {collection_name}")
        log_to_ui(f"Creating vector store collection: {collection_name}")
        
        persist_directory = os.path.join("chroma_storage", collection_name)
        vectorstore = Chroma(
            collection_name=collection_name, 
            embedding_function=azure_embeddings,
            persist_directory=persist_directory
        )
        
        # Create retriever with hierarchical support
        store = InMemoryStore()
        id_key = "doc_id"

        retriever = MultiVectorRetriever(
            vectorstore=vectorstore,
            docstore=store,
            id_key=id_key,
        )
        logger.info("Hierarchical MultiVectorRetriever initialized")

        # Prepare documents for hybrid search
        all_documents = []

        # Add content to retriever with hierarchical metadata
        log_to_ui("Adding hierarchical content to vector store...")

        if content['texts'] and text_summaries:
            log_to_ui(f"Adding {len(content['texts'])} text documents with hierarchy...")
            doc_ids = [str(uuid.uuid4()) for _ in content['texts']]

            # Create documents with enhanced metadata
            summary_texts = []
            for i, (summary, original_chunk) in enumerate(zip(text_summaries, content['texts'])):
                # Extract hierarchy metadata
                hierarchy_level = getattr(original_chunk.metadata, 'hierarchy_level', 'unknown') if hasattr(original_chunk, 'metadata') else 'unknown'
                parent_id = getattr(original_chunk.metadata, 'parent_id', None) if hasattr(original_chunk, 'metadata') else None
                child_id = getattr(original_chunk.metadata, 'child_id', None) if hasattr(original_chunk, 'metadata') else None

                metadata = {
                    id_key: doc_ids[i],
                    'content_type': 'text',
                    'hierarchy_level': hierarchy_level,
                    'parent_id': parent_id,
                    'child_id': child_id,
                    'chunk_index': i,
                    'source': pdf_filename or 'unknown'
                }

                doc = Document(page_content=summary, metadata=metadata)
                summary_texts.append(doc)
                all_documents.append(doc)

            retriever.vectorstore.add_documents(summary_texts)
            retriever.docstore.mset(list(zip(doc_ids, content['texts'])))
            logger.info(f"Added {len(content['texts'])} hierarchical text documents to vector store")
        
        if content['tables'] and table_summaries:
            log_to_ui(f"Adding {len(content['tables'])} table documents with hierarchy...")
            table_ids = [str(uuid.uuid4()) for _ in content['tables']]

            # Create table documents with enhanced metadata
            summary_tables = []
            for i, (summary, original_chunk) in enumerate(zip(table_summaries, content['tables'])):
                # Extract hierarchy metadata
                hierarchy_level = getattr(original_chunk.metadata, 'hierarchy_level', 'unknown') if hasattr(original_chunk, 'metadata') else 'unknown'
                parent_id = getattr(original_chunk.metadata, 'parent_id', None) if hasattr(original_chunk, 'metadata') else None
                child_id = getattr(original_chunk.metadata, 'child_id', None) if hasattr(original_chunk, 'metadata') else None

                metadata = {
                    id_key: table_ids[i],
                    'content_type': 'table',
                    'hierarchy_level': hierarchy_level,
                    'parent_id': parent_id,
                    'child_id': child_id,
                    'chunk_index': i,
                    'source': pdf_filename or 'unknown'
                }

                doc = Document(page_content=summary, metadata=metadata)
                summary_tables.append(doc)
                all_documents.append(doc)

            retriever.vectorstore.add_documents(summary_tables)
            retriever.docstore.mset(list(zip(table_ids, content['tables'])))
            logger.info(f"Added {len(content['tables'])} hierarchical table documents to vector store")
        
        if content['images'] and image_summaries:
            log_to_ui(f"Adding {len(content['images'])} image documents...")
            img_ids = [str(uuid.uuid4()) for _ in content['images']]

            # Create image documents with metadata
            summary_img = []
            for i, summary in enumerate(image_summaries):
                metadata = {
                    id_key: img_ids[i],
                    'content_type': 'image',
                    'hierarchy_level': 'standalone',
                    'chunk_index': i,
                    'source': pdf_filename or 'unknown'
                }

                doc = Document(page_content=summary, metadata=metadata)
                summary_img.append(doc)
                all_documents.append(doc)

            retriever.vectorstore.add_documents(summary_img)
            retriever.docstore.mset(list(zip(img_ids, content['images'])))
            logger.info(f"Added {len(content['images'])} image documents to vector store")

        # Create hybrid retriever (semantic + keyword search)
        log_to_ui("Setting up hybrid search (semantic + keyword)...")
        try:
            # Create BM25 retriever for keyword search
            bm25_retriever = BM25Retriever.from_documents(all_documents)
            bm25_retriever.k = 15  # Number of docs to retrieve

            # Create ensemble retriever combining semantic and keyword search
            ensemble_retriever = EnsembleRetriever(
                retrievers=[retriever, bm25_retriever],
                weights=[0.7, 0.3]  # 70% semantic, 30% keyword
            )

            logger.info("Hybrid search setup completed")
            log_to_ui("✅ Hybrid search (semantic + keyword) configured")

            # Store the ensemble retriever as the main retriever
            retriever.ensemble_retriever = ensemble_retriever

        except Exception as e:
            logger.warning(f"Failed to setup hybrid search: {e}")
            log_to_ui(f"⚠️ Hybrid search setup failed, using semantic search only: {e}")

        total_docs = len(content.get('texts', [])) + len(content.get('tables', [])) + len(content.get('images', []))
        completion_msg = f"Hierarchical vector store creation completed - {total_docs} total documents indexed"
        logger.info(completion_msg)
        log_to_ui(f"✅ Hierarchical vector store creation completed")
        log_to_ui(f"Total documents indexed: {total_docs}")

        return retriever, collection_name
        
    except Exception as e:
        error_msg = f"Vector store creation failed: {e}"
        logger.error(error_msg)
        log_to_ui(error_msg, "ERROR")
        st.error(f"❌ {error_msg}")
        return None, None

def get_existing_indices():
    """Get list of existing indices in chroma_storage"""
    try:
        if not os.path.exists("chroma_storage"):
            return []
        
        indices = []
        for item in os.listdir("chroma_storage"):
            item_path = os.path.join("chroma_storage", item)
            if os.path.isdir(item_path):
                indices.append(item)
        return sorted(indices)
    except Exception:
        return []

def load_existing_index(collection_name, config):
    """Load an existing vector store index"""
    try:
        from langchain_community.vectorstores import Chroma
        from langchain_openai import AzureOpenAIEmbeddings

        # Create embeddings
        azure_embeddings = AzureOpenAIEmbeddings(
            model=config['embedding_deployment'],
            azure_endpoint=config['endpoint'],
            api_version=config['api_version'],
            azure_ad_token_provider=lambda: config['azure_client_util'].get_azoai_client()[1]
        )

        # Load existing vector store
        persist_directory = os.path.join("chroma_storage", collection_name)
        vectorstore = Chroma(
            collection_name=collection_name,
            embedding_function=azure_embeddings,
            persist_directory=persist_directory
        )

        # Return the vectorstore directly since we can't restore the original documents
        # The summaries stored in the vectorstore will be used as context
        return vectorstore

    except Exception as e:
        st.error(f"❌ Failed to load index: {e}")
        return None

def process_query(question, retriever, config, chat_history=None, k=50):
    """Process user question through hierarchical RAG pipeline with hybrid search"""
    try:
        # Set up logging
        logger = setup_logging()
        logger.info(f"Processing query with hierarchical retrieval, requested chunk count: {k}")
        log_to_ui(f"Retrieving up to {k} relevant document chunks using hierarchical search...")

        # Check if retriever is a MultiVectorRetriever or regular vectorstore
        from langchain.retrievers.multi_vector import MultiVectorRetriever
        from langchain_community.vectorstores import Chroma

        # Retrieve documents using hierarchical approach
        docs = []

        if isinstance(retriever, MultiVectorRetriever):
            # Try hybrid search first if available
            if hasattr(retriever, 'ensemble_retriever'):
                try:
                    log_to_ui("Using hybrid search (semantic + keyword)...")
                    # Use ensemble retriever for hybrid search
                    hybrid_docs = retriever.ensemble_retriever.invoke(question, k=min(k, 100))
                    logger.info(f"Retrieved {len(hybrid_docs)} chunks using hybrid search")

                    # Get original documents from docstore
                    for doc in hybrid_docs:
                        if hasattr(doc, 'metadata') and 'doc_id' in doc.metadata:
                            original_doc = retriever.docstore.mget([doc.metadata['doc_id']])[0]
                            if original_doc:
                                docs.append(original_doc)

                except Exception as e:
                    logger.warning(f"Hybrid search failed, falling back to semantic: {e}")
                    log_to_ui(f"⚠️ Hybrid search failed, using semantic search")

            # Fallback to regular MultiVectorRetriever if hybrid failed or not available
            if not docs:
                docs = retriever.invoke(question, k=k)
                logger.info(f"Retrieved {len(docs)} chunks using MultiVectorRetriever")

        elif isinstance(retriever, Chroma):
            # Use regular vectorstore similarity search for loaded indices
            docs = retriever.similarity_search(question, k=k)
            logger.info(f"Retrieved {len(docs)} chunks using Chroma similarity_search")
        else:
            # Fallback - try invoke method
            docs = retriever.invoke(question, k=k)
            logger.info(f"Retrieved {len(docs)} chunks using generic invoke method")
        
        # Apply hierarchical retrieval logic
        enhanced_docs = _apply_hierarchical_retrieval(docs, retriever, logger)

        # Log the actual number of documents retrieved
        log_to_ui(f"Retrieved {len(docs)} initial chunks, enhanced to {len(enhanced_docs)} with hierarchical context")

        # If we got fewer documents than requested, log a warning
        if len(docs) < k:
            logger.warning(f"Retrieved fewer documents ({len(docs)}) than requested ({k})")
            log_to_ui(f"Note: Retrieved fewer chunks than requested. This may be due to document size or relevance filtering.")

        # Build context from enhanced documents
        context_text = ""
        if enhanced_docs:
            for doc in enhanced_docs:
                # Handle different document types
                if hasattr(doc, 'page_content'):
                    # LangChain Document object
                    context_text += doc.page_content + "\n\n"
                elif hasattr(doc, 'text'):
                    # Unstructured document object
                    context_text += doc.text + "\n\n"
                elif isinstance(doc, str):
                    # String content
                    context_text += doc + "\n\n"
                else:
                    # Try to convert to string
                    context_text += str(doc) + "\n\n"

        # Build conversation history context (last 3 conversations)
        conversation_context = ""
        if chat_history and len(chat_history) > 0:
            recent_conversations = chat_history[-5:]  # Get last 5 conversations
            conversation_context = "\nPrevious conversation:\n"
            for q, a, _ in recent_conversations:
                conversation_context += f"Q: {q}\nA: {a}\n\n"

        # Check if we have no context
        if not context_text.strip():
            return {
                "response": "I'm sorry, but I can't access or view the uploaded document. Please provide the text or key details from the document, and I'll be happy to help answer your question based on that information.",
                "context": {"images": [], "texts": []},
                "retrieved_docs": docs,
                "sources_count": len(docs) if docs else 0
            }
        
        # System prompt for drilling assistant role
        system_prompt = (
            "You are an expert drilling operations assistant with deep knowledge of drilling reports, "
            "well operations, and technical drilling parameters. Your expertise includes well control, "
            "drilling equipment, formation evaluation, and safety procedures. "
            "When answering questions about drilling reports:"
            "\n1. Provide technically precise information based only on the context provided"
            "\n2. Use proper drilling terminology and maintain technical accuracy"
            "\n3. Clearly state numerical values with their correct units"
            "\n4. Explain complex drilling concepts in a clear, accessible way"
            "\n5. When discussing safety or well control issues, emphasize their importance"
            "\n6. If information is not available in the context, clearly state this limitation"
            "\n7. Format responses with appropriate structure for readability"
            "\n8. Maintain a professional, informative tone appropriate for drilling professionals"
            "\n9. Don`t give any recommendations, just provide the answer."
        )


        # Create prompt
        user_prompt = f"""Answer the question based only on the following context, which can include text and information from the below tables.

Context: {context_text}{conversation_context}

Question: {question}

Answer:"""

        # Get response from Azure OpenAI
        azure_client_util = config['azure_client_util']
        deployment_name = config['deployment_name']

        client, _, _, _ = azure_client_util.get_azoai_client()
        response = client.chat.completions.create(
            model=deployment_name,
            messages=[ {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}],
            temperature=0.1,
            max_tokens=16000,
            presence_penalty=0.1,  # Add slight penalty to avoid repetition
            frequency_penalty=0.1  # Add slight penalty to encourage diversity
        )

        return {
            "response": response.choices[0].message.content,
            "context": {"images": [], "texts": enhanced_docs},
            "retrieved_docs": enhanced_docs,
            "sources_count": len(enhanced_docs) if enhanced_docs else 0
        }

    except Exception as e:
        st.error(f"❌ Query processing failed: {e}")
        return None

def delete_index(collection_name):
    """Delete an existing vector store index"""
    try:
        persist_directory = os.path.join("chroma_storage", collection_name)
        if os.path.exists(persist_directory):
            shutil.rmtree(persist_directory)
            return True
        return False
    except Exception as e:
        st.error(f"❌ Failed to delete index: {e}")
        return False

def get_pdf_hash(file_content):
    """Get hash of PDF file content"""
    return hashlib.md5(file_content).hexdigest()

def save_pdf_cache(pdf_hash, content, text_summaries, table_summaries, image_summaries):
    """Save processed PDF data to cache"""
    try:
        cache_dir = "pdf_cache"
        os.makedirs(cache_dir, exist_ok=True)
        
        cache_data = {
            'content': content,
            'text_summaries': text_summaries,
            'table_summaries': table_summaries,
            'image_summaries': image_summaries,
            'timestamp': datetime.now().isoformat()
        }
        
        cache_file = os.path.join(cache_dir, f"{pdf_hash}.pkl")
        with open(cache_file, 'wb') as f:
            pickle.dump(cache_data, f)
        return True
    except Exception as e:
        st.warning(f"⚠️ Failed to save cache: {e}")
        return False

def load_pdf_cache(pdf_hash):
    """Load processed PDF data from cache"""
    try:
        cache_file = os.path.join("pdf_cache", f"{pdf_hash}.pkl")
        if os.path.exists(cache_file):
            with open(cache_file, 'rb') as f:
                cache_data = pickle.load(f)
            return cache_data
        return None
    except Exception as e:
        st.warning(f"⚠️ Failed to load cache: {e}")
        return None

def get_cached_files():
    """Get list of cached PDF files"""
    try:
        cache_dir = "pdf_cache"
        if not os.path.exists(cache_dir):
            return []
        
        cached_files = []
        for file in os.listdir(cache_dir):
            if file.endswith('.pkl'):
                file_path = os.path.join(cache_dir, file)
                try:
                    with open(file_path, 'rb') as f:
                        cache_data = pickle.load(f)
                    cached_files.append({
                        'hash': file[:-4],  # Remove .pkl extension
                        'timestamp': cache_data.get('timestamp', 'Unknown'),
                        'size': os.path.getsize(file_path)
                    })
                except:
                    continue
        return sorted(cached_files, key=lambda x: x['timestamp'], reverse=True)
    except Exception:
        return []

def delete_cache_file(pdf_hash):
    """Delete a specific cache file"""
    try:
        cache_file = os.path.join("pdf_cache", f"{pdf_hash}.pkl")
        if os.path.exists(cache_file):
            os.remove(cache_file)
            return True
        return False
    except Exception as e:
        st.error(f"❌ Failed to delete cache: {e}")
        return False

def clear_all_cache():
    """Clear all cache files"""
    try:
        cache_dir = "pdf_cache"
        if os.path.exists(cache_dir):
            shutil.rmtree(cache_dir)
            os.makedirs(cache_dir, exist_ok=True)
            return True
        return False
    except Exception as e:
        st.error(f"❌ Failed to clear cache: {e}")
        return False

def _find_parent_for_child(child_chunk, parent_chunks):
    """Find the best matching parent chunk for a child chunk based on content overlap"""
    try:
        child_text = getattr(child_chunk, 'text', str(child_chunk))
        best_parent_id = "parent_0"  # Default fallback
        max_overlap = 0

        for i, parent_chunk in enumerate(parent_chunks):
            parent_text = getattr(parent_chunk, 'text', str(parent_chunk))

            # Simple overlap calculation based on common words
            child_words = set(child_text.lower().split())
            parent_words = set(parent_text.lower().split())
            overlap = len(child_words.intersection(parent_words))

            if overlap > max_overlap:
                max_overlap = overlap
                best_parent_id = f"parent_{i}"

        return best_parent_id
    except Exception:
        return "parent_0"  # Fallback to first parent

def _apply_hierarchical_retrieval(docs, retriever, logger):
    """Apply hierarchical retrieval logic to expand child documents with parent context"""
    try:
        from langchain.retrievers.multi_vector import MultiVectorRetriever

        enhanced_docs = []
        parent_ids_added = set()

        for doc in docs:
            enhanced_docs.append(doc)

            # Check if this is a child document and try to get its parent
            if hasattr(doc, 'metadata') and isinstance(retriever, MultiVectorRetriever):
                metadata = getattr(doc, 'metadata', {})
                hierarchy_level = metadata.get('hierarchy_level', 'unknown')
                parent_id = metadata.get('parent_id')

                # If this is a child document, try to retrieve its parent
                if hierarchy_level == 'child' and parent_id and parent_id not in parent_ids_added:
                    try:
                        # Search for parent document in the vectorstore
                        parent_docs = retriever.vectorstore.similarity_search(
                            f"parent_id:{parent_id}",
                            k=1,
                            filter={"parent_id": parent_id, "hierarchy_level": "parent"}
                        )

                        if parent_docs:
                            # Get the original parent document from docstore
                            parent_doc_id = parent_docs[0].metadata.get('doc_id')
                            if parent_doc_id:
                                parent_original = retriever.docstore.mget([parent_doc_id])[0]
                                if parent_original:
                                    enhanced_docs.append(parent_original)
                                    parent_ids_added.add(parent_id)
                                    logger.info(f"Added parent context for child document")
                    except Exception as e:
                        logger.warning(f"Failed to retrieve parent for child document: {e}")

        return enhanced_docs

    except Exception as e:
        logger.warning(f"Hierarchical retrieval enhancement failed: {e}")
        return docs  # Return original docs if enhancement fails

def main():
    """Main Streamlit application"""
    
    # Initialize session state
    initialize_session_state()
    
    # Setup logging
    setup_logging()
    
    # Header
    st.title("📄 Multimodal RAG Chatbot")
    st.markdown("Upload a PDF document and ask questions about its content using Azure OpenAI")
    
    # Setup environment
    config = setup_environment()
    if not config:
        st.stop()
    
    # Sidebar for PDF upload and processing
    with st.sidebar:
        st.header("📚 Load Existing Index")
        
        # Get existing indices
        existing_indices = get_existing_indices()
        
        if existing_indices:
            selected_index = st.selectbox(
                "Select an existing index:",
                options=[""] + existing_indices,
                help="Choose a previously processed document"
            )
            
            if selected_index:
                col1, col2 = st.columns(2)
                
                with col1:
                    if st.button("📂 Load Index", type="secondary"):
                        with st.spinner("Loading index..."):
                            retriever = load_existing_index(selected_index, config)
                            if retriever:
                                st.session_state.retriever = retriever
                                st.session_state.pdf_processed = True
                                st.session_state.document_info = {
                                    'index_name': selected_index,
                                    'loaded_from_storage': True
                                }
                                st.success(f"✅ Loaded index: {selected_index}")
                                st.rerun()
                
                with col2:
                    if st.button("🗑️ Delete Index", type="secondary"):
                        if st.session_state.get('confirm_delete') == selected_index:
                            if delete_index(selected_index):
                                st.success(f"✅ Deleted index: {selected_index}")
                                if 'confirm_delete' in st.session_state:
                                    del st.session_state.confirm_delete
                                st.rerun()
                            else:
                                st.error("❌ Failed to delete index")
                        else:
                            st.session_state.confirm_delete = selected_index
                            st.warning("⚠️ Click again to confirm deletion")
        else:
            st.info("No existing indices found.")
        
        st.divider()
        
        st.header("🗄️ Cache Management")
        
        # Get cached files
        cached_files = get_cached_files()
        
        if cached_files:
            st.write(f"**Cached files:** {len(cached_files)}")
            
            # Show latest cache info
            latest_cache = cached_files[0]
            st.write(f"**Latest:** {latest_cache['timestamp'][:19]}")
            st.write(f"**Total size:** {sum(f['size'] for f in cached_files) / 1024:.1f} KB")
            
            if st.button("🧹 Clear All Cache", type="secondary"):
                if clear_all_cache():
                    st.success("✅ Cache cleared successfully!")
                    st.rerun()
        else:
            st.info("No cached files found.")
        
        st.divider()
        
        st.header("📁 Upload New Document")
        
        uploaded_file = st.file_uploader(
            "Upload a PDF file",
            type=['pdf'],
            help="Upload a PDF document to analyze and ask questions about"
        )
        
        if uploaded_file is not None:
            # Get file size
            file_size_mb = len(uploaded_file.getvalue()) / (1024 * 1024)
            st.success(f"✅ File uploaded: {uploaded_file.name} ({file_size_mb:.1f} MB)")
            
            # Show batch size slider for large files
            if file_size_mb > 2:
                st.warning(f"⚠️ Large file detected ({file_size_mb:.1f} MB). Processing will use batching.")
                st.session_state.batch_size = st.slider(
                    "Batch Size",
                    min_value=10,
                    max_value=100,
                    value=st.session_state.batch_size,
                    step=5,
                    help="Number of items to process in each batch for large files"
                )
            
            # Process PDF button
            if st.button("🔄 Process Document", type="primary"):
                # Get PDF hash for caching
                pdf_content = uploaded_file.getvalue()
                pdf_hash = get_pdf_hash(pdf_content)
                
                # Check cache first
                cached_data = load_pdf_cache(pdf_hash)
                
                if cached_data:
                    st.info("📋 Found cached data, skipping PDF processing and summary generation...")
                    content = cached_data['content']
                    text_summaries = cached_data['text_summaries']
                    table_summaries = cached_data['table_summaries']
                    image_summaries = cached_data['image_summaries']
                    
                    with st.spinner("Creating vector store..."):
                        retriever, collection_name = create_vector_store(content, text_summaries, table_summaries, image_summaries, config, uploaded_file.name)
                    
                    if retriever:
                        st.session_state.retriever = retriever
                        st.session_state.pdf_processed = True
                        st.session_state.document_info = {
                            'filename': uploaded_file.name,
                            'index_name': collection_name,
                            'total_chunks': content['total_chunks'],
                            'text_chunks': content['text_chunks'],
                            'table_chunks': content['table_chunks'],
                            'image_chunks': content['image_chunks'],
                            'newly_created': True,
                            'from_cache': True
                        }
                        st.success("✅ Document processed successfully from cache!")
                        st.rerun()
                else:
                    with st.spinner("Processing PDF..."):
                        # Process PDF
                        content = process_pdf(uploaded_file, config)
                        
                        if content:
                            # Use batch processing for large files
                            batch_size = st.session_state.batch_size if file_size_mb > 2 else 25
                            text_summaries, table_summaries, image_summaries = generate_summaries(content, config, batch_size)
                            
                            if text_summaries is not None:
                                # Save to cache
                                save_pdf_cache(pdf_hash, content, text_summaries, table_summaries, image_summaries)
                                
                                with st.spinner("Creating vector store..."):
                                    retriever, collection_name = create_vector_store(content, text_summaries, table_summaries, image_summaries, config, uploaded_file.name)
                                
                                if retriever:
                                    st.session_state.retriever = retriever
                                    st.session_state.pdf_processed = True
                                    st.session_state.document_info = {
                                        'filename': uploaded_file.name,
                                        'index_name': collection_name,
                                        'total_chunks': content['total_chunks'],
                                        'text_chunks': content['text_chunks'],
                                        'table_chunks': content['table_chunks'],
                                        'image_chunks': content['image_chunks'],
                                        'newly_created': True
                                    }
                                    st.success("✅ Document processed successfully!")
                                    st.rerun()
        
        # Document info
        if st.session_state.pdf_processed:
            st.header("📊 Document Info")
            info = st.session_state.document_info
            
            if info.get('loaded_from_storage'):
                st.write(f"**Index:** {info['index_name']}")
                st.write("**Status:** 📂 Loaded from storage")
            elif info.get('newly_created'):
                st.write(f"**File:** {info['filename']}")
                st.write(f"**Index:** {info['index_name']}")
                if info.get('from_cache'):
                    st.write("**Status:** 📋 Created from cache")
                else:
                    st.write("**Status:** 🆕 Newly created")
                st.write(f"**Total chunks:** {info['total_chunks']}")
                st.write(f"**Text chunks:** {info['text_chunks']}")
                st.write(f"**Table chunks:** {info['table_chunks']}")
                st.write(f"**Image chunks:** {info['image_chunks']}")
            
            if st.button("🗑️ Clear Document"):
                st.session_state.retriever = None
                st.session_state.pdf_processed = False
                st.session_state.chat_history = []
                st.session_state.document_info = {}
                st.rerun()
        
        # Process Logs Section
        if st.session_state.process_logs:
            st.divider()
            st.header("📋 Process Logs")
            
            # Log display options
            col1, col2 = st.columns(2)
            with col1:
                if st.button("🧹 Clear Logs", type="secondary"):
                    clear_logs()
                    st.rerun()
            with col2:
                show_all_logs = st.toggle("Show All", value=False)
            
            # Display logs
            logs_to_show = st.session_state.process_logs if show_all_logs else st.session_state.process_logs[-10:]
            
            # Create a scrollable container for logs
            log_container = st.container()
            with log_container:
                for log in logs_to_show:
                    if "ERROR" in log:
                        st.error(log)
                    elif "WARNING" in log:
                        st.warning(log)
                    else:
                        st.text(log)
    
    # Main chat interface
    if not st.session_state.pdf_processed:
        st.info("👆 Please upload and process a PDF document to start asking questions.")
        
        # Sample questions section
        st.header("🎯 How to use this chatbot")
        st.markdown("""
        1. **Upload a PDF** in the sidebar
        2. **Click "Process Document"** to analyze it
        3. **Ask questions** about the content
        
        **Example questions you can ask:**
        - What is the main topic of this document?
        - Summarize the key findings
        - What are the conclusions?
        - Extract specific data points
        - Explain any tables or charts
        """)
        
    else:
        st.success("✅ Document processed! You can now ask questions about the content.")
        
        # Chat settings
        with st.sidebar:
            st.divider()
            st.header("⚙️ Chat Settings")
            st.session_state.retrieval_count = st.slider(
                "Number of Document Chunks to Retrieve",
                min_value=5,
                max_value=100,
                value=st.session_state.retrieval_count,
                step=5,
                help="How many document chunks to retrieve for answering questions (hierarchical retrieval may expand this)"
            )

            # Add hierarchical retrieval info
            st.info("🔗 **Hierarchical Retrieval Active**\n\n"
                   "• Parent-child document relationships\n"
                   "• Metadata-based filtering\n"
                   "• Hybrid search (semantic + keyword)\n"
                   "• Automatic context expansion")
        
        # Chat interface
        st.header("💬 Chat with your Document")
        
        # Display chat history
        for question, answer, sources_count in st.session_state.chat_history:
            with st.container():
                # User question
                with st.chat_message("user"):
                    st.write(question)
                
                # Assistant response
                with st.chat_message("assistant"):
                    st.write(answer)
                    st.caption(f"📚 Based on {sources_count} document chunks")
        
        # Chat input
        if prompt := st.chat_input("Ask a question about your document..."):
            # Add user message to chat
            with st.chat_message("user"):
                st.write(prompt)
            
            # Process query and add assistant response
            with st.chat_message("assistant"):
                with st.spinner("Thinking..."):
                    result = process_query(prompt, st.session_state.retriever, config, st.session_state.chat_history, k=st.session_state.retrieval_count)
                    
                    if result:
                        st.write(result['response'])
                        st.caption(f"📚 Based on {result['sources_count']} document chunks (requested: {st.session_state.retrieval_count})")
                        
                        # Add to chat history
                        st.session_state.chat_history.append((
                            prompt, 
                            result['response'], 
                            result['sources_count']
                        ))
        
        # Clear chat button
        if st.session_state.chat_history:
            if st.button("🧹 Clear Chat History"):
                st.session_state.chat_history = []
                st.rerun()

if __name__ == "__main__":
    main() 







